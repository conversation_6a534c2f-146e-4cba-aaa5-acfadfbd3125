import numpy as np
import matplotlib as mpl
import matplotlib.pyplot as plt

def plot_beeswarm_panel_a(feature_impacts, feature_values,
                          feature_order=None,
                          output="panel_a_beeswarm",
                          figsize=(10, 2.2),  # 宽屏长条版面
                          xlim=(-25, 65)):
    """
    feature_impacts: dict[str, 1D array-like]  每行点的横坐标（影响值，单位 cycles）
    feature_values:  dict[str, 1D array-like]  每行点的着色值（任意范围，内部会归一化）
    feature_order:   list[str]                 绘制顺序（从上到下），默认使用 dict 顺序
    """

    # 排序（从上到下）
    if feature_order is None:
        feature_order = list(feature_impacts.keys())

    # 配色：浅灰 -> 渐变蓝（接近原图）
    cmap = mpl.colors.LinearSegmentedColormap.from_list(
        "grey_blue", ["#e9ecef", "#8fb7f0", "#3d7be0"], N=256
    )

    # 将所有着色值拼接，以便做全局归一化
    all_vals = np.concatenate([np.asarray(feature_values[f]).ravel()
                               for f in feature_order if len(feature_values[f]) > 0])
    # 若全为常数，避免除零
    vmin, vmax = float(np.min(all_vals)), float(np.max(all_vals))
    if vmax - vmin < 1e-12:
        vmin, vmax = vmin - 0.5, vmax + 0.5
    norm = mpl.colors.Normalize(vmin=vmin, vmax=vmax)

    fig, ax = plt.subplots(figsize=figsize, dpi=200)
    # 让左侧长标签留出空间，右侧给色条留边
    plt.subplots_adjust(left=0.30, right=0.86, top=0.92, bottom=0.28)

    # 纵坐标：从上到下
    y_positions = np.arange(len(feature_order))[::-1]

    # 垂直抖动幅度（控制“蜂群”厚度）
    jitter = 0.28
    dot_size = 18  # 点尺寸
    alpha_base = 0.9

    for yi, feat in zip(y_positions, feature_order):
        x = np.asarray(feature_impacts[feat]).ravel()
        cvals = np.asarray(feature_values[feat]).ravel()

        # 与 x 同长度的抖动
        y = yi + (np.random.rand(len(x)) - 0.5) * 2 * jitter

        ax.scatter(
            x, y,
            c=cvals, cmap=cmap, norm=norm,
            s=dot_size, alpha=alpha_base, linewidths=0,
        )

    # x=0 参照线
    ax.axvline(0, color="#8e8e8e", lw=1.0, alpha=0.8, zorder=0)

    # 网格（水平虚线，淡灰）
    ax.grid(axis="y", color="#d0d7de", linestyle="--", linewidth=0.8, alpha=0.6)
    ax.grid(axis="x", visible=False)

    # 轴范围与刻度
    ax.set_xlim(*xlim)
    # 只显示几个关键刻度，接近示例图
    ax.set_xticks([-20, 0, 20, 40, 60])
    ax.set_xlabel("Impact on EFC prediction (cycles)", fontsize=10)

    ax.set_yticks(y_positions)
    ax.set_yticklabels(feature_order, fontsize=9)

    # 去除上右边框，左下变淡
    for spine in ["top", "right"]:
        ax.spines[spine].set_visible(False)
    for spine in ["left", "bottom"]:
        ax.spines[spine].set_color("#b9c0c7")
        ax.spines[spine].set_linewidth(0.8)

    # 色条（竖直，右侧，标注“Feature value”，上下标 Low/High）
    mappable = mpl.cm.ScalarMappable(norm=norm, cmap=cmap)
    cbar = fig.colorbar(mappable, ax=ax, pad=0.02, fraction=0.08, aspect=30)
    cbar.set_label("Feature value", rotation=90, fontsize=9)
    # 只给出 Low / High 两端标注
    cbar.set_ticks([norm.vmin, norm.vmax])
    cbar.set_ticklabels(["Low", "High"])
    cbar.ax.tick_params(labelsize=8)

    # 紧致布局与导出
    plt.tight_layout()
    for ext in ["png", "svg", "pdf"]:
        plt.savefig(f"{output}.{ext}", dpi=600 if ext == "png" else None,
                    bbox_inches="tight")
    plt.show()


if __name__ == "__main__":
    # ===== 示例数据（用于预览，替换为你的真实数据即可）=====
    rng = np.random.default_rng(42)
    features = [
        "Maximum instantaneous discharge current",
        "Rest fraction at high SOC",
        r"Peak frequency $f_1$",
        "Rest SOC",
        "Normalized current variance",
        "Relative charge fraction",
        r"Peak frequency $f_2$",
    ]

    # 每个特征 60 个点，均值/方差设置为与示例图相近的分布形态
    means = [35, 18, 12, 5, 15, 22, -5]
    stds  = [7, 10, 9, 8, 8, 7, 6]

    feature_impacts = {}
    feature_values  = {}
    for feat, mu, sd in zip(features, means, stds):
        x = rng.normal(mu, sd, size=60)
        # 让一部分点落到负区间，增强对称性
        if mu > 0:
            x[:10] -= (abs(mu) + 15)

        # 特征值（用于着色），0-1 内分布；与 x 略有关联，提升“颜色趋势”效果
        raw_val = rng.normal(0.5 + 0.015 * (x - mu), 0.18, size=x.size)
        vals = np.clip(raw_val, 0, 1)

        feature_impacts[feat] = x
        feature_values[feat] = vals

    plot_beeswarm_panel_a(feature_impacts, feature_values,
                          feature_order=features,
                          output="figure_a_beeswarm",
                          figsize=(10, 2.2),
                          xlim=(-25, 65))