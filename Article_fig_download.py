"""
学术论文图片下载工具

这个脚本可以从各种学术论文网站下载文章图片，支持以下网站:
1. Nature系列期刊 (nature.com): 直接下载文章中的所有图片
2. ScienceDirect期刊: 通过保存的HTML文件下载图片
3. arXiv (arxiv.org): 下载PDF文件
4. Scientific American (scientificamerican.com): 支持部分格式图片下载
5. Springer (springer.com): 支持部分格式图片下载

使用说明:
1. 直接运行: python Article_fig_download.py
   - 修改脚本中的paper_url变量为您要下载的文章URL

2. 命令行参数:
   - 对于ScienceDirect文章:
     python Article_fig_download.py --html 本地HTML文件路径 --url 原始文章URL

ScienceDirect使用说明:
由于ScienceDirect网站的访问限制，请按照以下步骤下载图片:
1. 使用浏览器打开ScienceDirect文章页面
2. 右键点击页面，选择"另存为"或"Save as"，保存为完整网页(HTML)
3. 运行: python Article_fig_download.py --html 保存的文件路径.html --url 原始文章URL
"""

import os
import re
import requests
import json
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, parse_qs, unquote

def sanitize_filename(name):
    """
    清理字符串，使其可以安全地用作 Windows 和 Linux/macOS 的文件名/文件夹名。
    """
    sanitized = re.sub(r'[\\/*?:"<>|]', "_", name)
    sanitized = sanitized.strip('. ')
    return sanitized

def download_sciencedirect_from_html(html_file_path, base_url=None):
    """
    从本地保存的ScienceDirect文章HTML文件中提取图片并下载
    
    :param html_file_path: 本地保存的HTML文件路径
    :param base_url: 原始文章URL，用于构建完整的图片URL
    :return: 下载的图片数量
    """
    print(f"▶️ 开始从本地HTML文件处理: {html_file_path}")
    
    try:
        # 读取HTML文件
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取文章标题和DOI
        title = None
        doi = None
        article_id = None
        abstract = None
        
        # 获取标题
        title_selectors = [
            'h1.article-title',
            'span.title-text',
            'h1.title',
            'h2.title',
            'title'  # 如果其他选择器都没找到，尝试使用页面标题
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.text.strip()
                # 清理掉标题中可能包含的网站名称
                title = re.sub(r'\s*\|\s*ScienceDirect.*$', '', title)
                break
        
        if not title:
            title = "Untitled_ScienceDirect_Article"
        
        # 获取DOI
        doi_selectors = [
            'a.doi',
            'a[href*="doi.org"]',
            'div.doi'
        ]
        
        for selector in doi_selectors:
            doi_elem = soup.select_one(selector)
            if doi_elem:
                doi_text = doi_elem.text.strip()
                # 提取DOI
                doi_match = re.search(r'10\.\d{4,9}/[-._;()/:a-zA-Z0-9]+', doi_text)
                if doi_match:
                    doi = doi_match.group(0)
                    break
        
        # 提取摘要
        abstract_selectors = [
            'div#Abs1-content',
            'div.c-article-section__content p',
            'div[id^="Abs"]',
            'section.abstract',
            'div.abstract',
            'p.abstract',
            'div[id*="abstract"]',
            'div.c-article-body p:first-of-type',
            'meta[name="description"]',
            'div.c-article-section:first-of-type p',
            'div[data-test="abstract"]',
            'p[data-test="abstractText"]',
            'div#abstract-content',
            'div.article__abstract',
            'div.article-abstract'
        ]
        
        for selector in abstract_selectors:
            abstract_elem = soup.select_one(selector)
            if abstract_elem:
                if selector == 'meta[name="description"]' and 'content' in abstract_elem.attrs:
                    abstract = abstract_elem['content'].strip()
                else:
                    abstract = abstract_elem.text.strip()
                # 清理摘要文本
                abstract = clean_abstract_text(abstract)
                # 如果摘要太短，可能不是真正的摘要
                if len(abstract) > 100:
                    break
        
        # 如果上面的选择器都没找到摘要，尝试查找包含"Abstract"的段落
        if not abstract or len(abstract) < 100:
            paragraphs = soup.select('p')
            for p in paragraphs:
                if 'Abstract' in p.text:
                    abstract = clean_abstract_text(p.text.strip())
                    if len(abstract) > 100:
                        break
        
        # 如果仍然没找到摘要，尝试查找文章的第一段
        if not abstract or len(abstract) < 100:
            # 尝试找到文章正文的第一段
            article_sections = soup.select('div.c-article-section')
            for section in article_sections:
                # 跳过摘要部分
                if 'abstract' in section.get('id', '').lower() or 'abstract' in section.get('class', [''])[0].lower():
                    continue
                
                # 获取第一个段落
                first_p = section.select_one('p')
                if first_p and len(first_p.text.strip()) > 100:
                    abstract = first_p.text.strip()
                    break
        
        # 提取文章ID
        # 从base_url中提取
        if base_url:
            pii_match = re.search(r'pii/([^?&#]+)', base_url, re.IGNORECASE)
            if pii_match:
                article_id = pii_match.group(1)
        
        # 如果没有从URL中找到，尝试从页面元素获取
        if not article_id:
            # 尝试从meta标签获取
            meta_pii = soup.select_one('meta[name="citation_pii"]')
            if meta_pii and 'content' in meta_pii.attrs:
                article_id = meta_pii['content']
                
        # 最后的备选方案，使用文件名的一部分
        if not article_id:
            article_id = os.path.basename(html_file_path).replace('.html', '')
        
        print(f"📝 文章标题: {title}")
        print(f"🔑 文章ID: {article_id}")
        if doi:
            print(f"🔖 DOI: {doi}")
        if abstract:
            print(f"📄 成功提取摘要 ({len(abstract)} 字符)")
        
        # 创建文件夹 (使用文章ID和标题)
        short_title = title[:30] if len(title) > 30 else title
        folder_name = sanitize_filename(f"{article_id}_{short_title}")
        os.makedirs(folder_name, exist_ok=True)
        print(f"📂 已创建/指定文件夹: '{folder_name}'")
        
        # 保存文章信息
        info_path = os.path.join(folder_name, "article_info.txt")
        with open(info_path, 'w', encoding='utf-8') as f:
            f.write(f"标题: {title}\n")
            if doi:
                f.write(f"DOI: {doi}\n")
            if base_url:
                f.write(f"URL: {base_url}\n")
            f.write(f"ID: {article_id}\n")
            if abstract:
                f.write(f"\n摘要:\n{abstract}\n")
        
        # 查找所有图像容器
        downloaded_count = 0
        downloaded_urls = set()  # 用于跟踪已下载的URL，避免重复
        
        # --- 查找图片方式1: 直接在页面中查找图片元素 ---
        image_containers = []
        container_selectors = [
            'div.figure',
            'figure',
            'div.fig',
            'div[class*="figure"]',
            'div.image',
            'div.graphic'
        ]
        
        for selector in container_selectors:
            containers = soup.select(selector)
            if containers:
                image_containers.extend(containers)
                
        print(f"🔍 找到 {len(image_containers)} 个图片容器，开始处理...")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        }
        
        # 处理页面中的图片
        for container in image_containers:
            # 尝试获取图片编号
            fig_number = None
            img_title = None
            
            # 尝试获取图片标签/编号
            label_selectors = [
                '.label',
                '.caption-label',
                'figcaption',
                '.figure-label',
                '.fig-label'
            ]
            
            for selector in label_selectors:
                label_elem = container.select_one(selector)
                if label_elem:
                    label_text = label_elem.text.strip()
                    # 查找图表编号 (Fig. 1, Figure 2, etc.)
                    fig_match = re.search(r'(?:fig(?:ure|\.)?)\s*(\d+[a-z]?)', label_text, re.IGNORECASE)
                    if fig_match:
                        fig_number = fig_match.group(1)
                        break
            
            # 尝试从容器的ID或class属性中提取图片编号
            if not fig_number:
                for attr in ['id', 'class']:
                    if attr in container.attrs:
                        attr_value = ' '.join(container[attr]) if isinstance(container[attr], list) else container[attr]
                        fig_match = re.search(r'fig(?:ure)?[-_]?(\d+[a-z]?)', attr_value, re.IGNORECASE)
                        if fig_match:
                            fig_number = fig_match.group(1)
                            break
            
            # 尝试查找标题
            caption_selectors = [
                '.caption',
                'figcaption',
                '.figure-caption',
                '.legend',
                '.graphic-legend'
            ]
            
            for selector in caption_selectors:
                caption_elem = container.select_one(selector)
                if caption_elem:
                    caption_text = caption_elem.text.strip()
                    # 尝试提取标题 (去除图编号部分)
                    title_match = re.search(r'(?:fig(?:ure|\.)?)\s*\d+[a-z]?[\.:\s]+(.*)', caption_text, re.IGNORECASE)
                    if title_match:
                        img_title = title_match.group(1).strip()
                    else:
                        # 如果没有匹配到标准格式，就使用整个文本
                        img_title = caption_text
                    break
            
            # 如果仍然没有找到编号，分配一个序号
            if not fig_number:
                fig_number = str(downloaded_count + 1)
            
            # 查找图片元素
            img_elements = container.select('img')
            
            # 可能还有一些图片是通过背景图片设置的
            background_elements = container.select('[style*="background"]')
            for elem in background_elements:
                if 'style' in elem.attrs:
                    style = elem['style']
                    bg_match = re.search(r'background(?:-image)?:\s*url\([\'"]?(.*?)[\'"]?\)', style)
                    if bg_match:
                        img_url = bg_match.group(1)
                        if img_url:
                            # 创建一个虚拟img元素
                            img_tag = soup.new_tag('img')
                            img_tag['src'] = img_url
                            img_elements.append(img_tag)
            
            # 如果找到了图片，下载它们
            for img in img_elements:
                if 'src' in img.attrs:
                    img_url = img['src']
                    
                    # 确保URL是完整的
                    if not img_url.startswith(('http://', 'https://')):
                        # 如果有base_url，使用它构建完整URL
                        if base_url:
                            img_url = urljoin(base_url, img_url)
                        else:
                            # 如果没有base_url，跳过相对URL
                            print(f"⚠️ 跳过相对URL: {img_url} (未提供base_url)")
                            continue
                    
                    # 处理ScienceDirect特有的图片URL模式
                    if 'sciencedirect.com' in img_url:
                        # 尝试获取高分辨率版本
                        # 将小尺寸图片链接转换为大尺寸
                        img_url = img_url.replace('_small.jpg', '.jpg')
                        img_url = img_url.replace('_small.png', '.png')
                        img_url = img_url.replace('_low.jpg', '.jpg')
                        img_url = img_url.replace('_low.png', '.png')
                        img_url = img_url.replace('_thumbnail.jpg', '.jpg')
                        img_url = img_url.replace('_thumbnail.png', '.png')
                    
                    # 如果这个URL已经下载过，跳过
                    if img_url in downloaded_urls:
                        continue
                    
                    try:
                        # 构造文件名
                        if img_title:
                            # 限制标题长度
                            short_img_title = img_title[:50] if len(img_title) > 50 else img_title
                            base_filename = f"Fig{fig_number}_{sanitize_filename(short_img_title)}"
                        else:
                            base_filename = f"Fig{fig_number}"
                        
                        # 从URL中获取文件扩展名
                        file_ext = os.path.splitext(urlparse(img_url).path)[1]
                        if not file_ext or len(file_ext) > 5:  # 防止异常的扩展名
                            file_ext = '.jpg'  # 默认扩展名
                        
                        # 构造文件名
                        filename = sanitize_filename(f"{base_filename}{file_ext}")
                        save_path = os.path.join(folder_name, filename)
                        
                        # 下载图片
                        print(f"⬇️ 正在下载图片: {img_url}")
                        img_response = requests.get(img_url, headers=headers, stream=True, timeout=15)
                        img_response.raise_for_status()
                        
                        with open(save_path, 'wb') as f:
                            for chunk in img_response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        
                        print(f"   ✅ 下载成功: {filename}")
                        downloaded_count += 1
                        downloaded_urls.add(img_url)
                        
                        # 避免请求过于频繁
                        time.sleep(0.5)
                    
                    except Exception as e:
                        print(f"   ❌ 下载失败: {img_url} | 原因: {e}")
        
        # --- 查找图片方式2: 查找JavaScript中的图片数据 ---
        # ScienceDirect经常在JavaScript中包含图片数据
        script_tags = soup.find_all('script')
        image_data_found = False
        
        for script in script_tags:
            if script.string and ('article' in script.string and 'figure' in script.string):
                # 尝试提取JSON数据
                try:
                    # 添加更多模式以匹配不同的JSON格式
                    json_matches = []
                    
                    # 模式1: 常见的figures数组格式
                    pattern1 = re.findall(r'({[^{]*?"figure(?:s|List)?"\s*:\s*\[.*?\].*?})', script.string)
                    if pattern1:
                        json_matches.extend(pattern1)
                    
                    # 模式2: 嵌套在article对象内的figures格式
                    pattern2 = re.findall(r'{"article".*?"figures"\s*:\s*(\[.*?\])', script.string)
                    if pattern2:
                        for p in pattern2:
                            json_matches.append('{"figures":' + p + '}')
                    
                    # 模式3: 变量赋值格式 (var xxx = {...})
                    pattern3 = re.search(r'var\s+\w+\s*=\s*({.*"figures"\s*:.*})', script.string)
                    if pattern3:
                        json_matches.append(pattern3.group(1))
                    
                    # 如果没有找到匹配，尝试直接将整个脚本作为JSON解析
                    if not json_matches and '{' in script.string and '}' in script.string:
                        # 尝试找到第一个 { 和最后一个 }
                        start = script.string.find('{')
                        end = script.string.rfind('}') + 1
                        if start >= 0 and end > start:
                            json_matches.append(script.string[start:end])
                    
                    for json_str in json_matches:
                        # 清理并解析JSON
                        try:
                            # 尝试找到JSON对象的开始和结束
                            print(f"🔍 找到可能的JSON数据，尝试解析...")
                            
                            # 首先尝试直接解析
                            try:
                                data = json.loads(json_str)
                            except json.JSONDecodeError:
                                # 如果直接解析失败，尝试更精确地提取JSON对象
                                start_idx = json_str.find('{')
                                if start_idx == -1:
                                    continue
                                    
                                # 确保JSON格式正确
                                balanced_count = 0
                                for i, char in enumerate(json_str[start_idx:]):
                                    if char == '{':
                                        balanced_count += 1
                                    elif char == '}':
                                        balanced_count -= 1
                                        if balanced_count == 0:
                                            json_str = json_str[start_idx:start_idx+i+1]
                                            break
                                
                                data = json.loads(json_str)
                            
                            # 查找图片数据 - 支持多种格式
                            figures = None
                            
                            # 1. 直接的figures数组
                            if 'figures' in data:
                                figures = data['figures']
                            # 2. 单个figure对象
                            elif 'figure' in data:
                                figures = [data['figure']]
                            # 3. figureList数组
                            elif 'figureList' in data:
                                figures = data['figureList']
                            # 4. 嵌套在article对象中
                            elif 'article' in data and isinstance(data['article'], dict):
                                article_data = data['article']
                                if 'figures' in article_data:
                                    figures = article_data['figures']
                                elif 'figure' in article_data:
                                    figures = [article_data['figure']]
                            
                            if figures:
                                image_data_found = True
                                print(f"🔍 从JavaScript中找到 {len(figures)} 个图片数据")
                                
                                for i, figure in enumerate(figures):
                                    # 跳过非字典类型的数据
                                    if not isinstance(figure, dict):
                                        continue
                                        
                                    # 提取图片URL
                                    img_url = None
                                    for key in ['src', 'href', 'downloadUrl', 'highResUrl', 'largeUrl', 'url']:
                                        if key in figure and figure[key]:
                                            img_url = figure[key]
                                            break
                                    
                                    # 检查links数组
                                    if not img_url and 'links' in figure:
                                        for link in figure.get('links', []):
                                            if isinstance(link, dict) and (link.get('rel') == 'full' or link.get('type') in ['image/jpeg', 'image/png']):
                                                img_url = link.get('href')
                                                break
                                    
                                    # 如果没有找到URL，跳过此图片
                                    if not img_url:
                                        continue
                                    
                                    # 确保URL是完整的
                                    if not img_url.startswith(('http://', 'https://')):
                                        if base_url:
                                            img_url = urljoin(base_url, img_url)
                                        else:
                                            # 如果没有base_url，跳过相对URL
                                            print(f"⚠️ 跳过相对URL: {img_url} (未提供base_url)")
                                            continue
                                    
                                    # 如果这个URL已经下载过，跳过
                                    if img_url in downloaded_urls:
                                        continue
                                    
                                    try:
                                        # 获取图片编号和标题
                                        fig_number = None
                                        img_title = None
                                        
                                        if 'label' in figure:
                                            label = figure['label']
                                            fig_match = re.search(r'(?:fig(?:ure|\.)?)\s*(\d+[a-z]?)', label, re.IGNORECASE)
                                            if fig_match:
                                                fig_number = fig_match.group(1)
                                        
                                        if 'title' in figure:
                                            img_title = figure['title']
                                        elif 'caption' in figure:
                                            img_title = figure['caption']
                                            # 移除HTML标签
                                            img_title = re.sub(r'<[^>]+>', '', img_title)
                                        
                                        # 如果没有找到编号，使用索引
                                        if not fig_number:
                                            fig_number = str(downloaded_count + 1)
                                        
                                        # 构造文件名
                                        if img_title:
                                            # 限制标题长度
                                            short_img_title = img_title[:50] if len(img_title) > 50 else img_title
                                            base_filename = f"Fig{fig_number}_{sanitize_filename(short_img_title)}"
                                        else:
                                            base_filename = f"Fig{fig_number}"
                                        
                                        # 从URL中获取文件扩展名
                                        file_ext = os.path.splitext(urlparse(img_url).path)[1]
                                        if not file_ext or len(file_ext) > 5:
                                            file_ext = '.jpg'
                                        
                                        # 构造文件名
                                        filename = sanitize_filename(f"{base_filename}{file_ext}")
                                        save_path = os.path.join(folder_name, filename)
                                        
                                        # 下载图片
                                        print(f"⬇️ 正在下载图片(JavaScript数据): {img_url}")
                                        img_response = requests.get(img_url, headers=headers, stream=True, timeout=15)
                                        img_response.raise_for_status()
                                        
                                        with open(save_path, 'wb') as f:
                                            for chunk in img_response.iter_content(chunk_size=8192):
                                                f.write(chunk)
                                        
                                        print(f"   ✅ 下载成功: {filename}")
                                        downloaded_count += 1
                                        downloaded_urls.add(img_url)
                                        
                                        # 避免请求过于频繁
                                        time.sleep(0.5)
                                        
                                    except Exception as e:
                                        print(f"   ❌ 下载失败: {img_url} | 原因: {e}")
                        
                        except json.JSONDecodeError:
                            continue
                except Exception:
                    continue
        
        print(f"\n🎉 处理完成！总共下载了 {downloaded_count} 张图片到文件夹 '{folder_name}'。")
        return downloaded_count
        
    except Exception as e:
        print(f"❌ 处理HTML文件失败: {e}")
        return 0

def download_sciencedirect_images(url):
    """
    从ScienceDirect文章页面下载所有图片
    
    :param url: ScienceDirect文章页面URL
    :return: 下载的图片数量
    """
    print(f"▶️ 开始处理URL: {url}")
    print("⚠️ ScienceDirect网站有访问限制，无法直接通过脚本下载图片。")
    print("💡 建议使用浏览器访问文章页面，将完整网页保存为HTML文件，然后使用 --html 选项处理保存的文件。")
    print("  例如: python Article_fig_download.py --html saved_article.html")
    return 0

def clean_abstract_text(text):
    """
    清理摘要文本，去除多余的前缀和格式化问题
    
    :param text: 原始摘要文本
    :return: 清理后的摘要文本
    """
    if not text:
        return text
    
    # 去除"Abstract:"或"Abstract"前缀
    text = re.sub(r'^Abstract\s*:?\s*', '', text, flags=re.IGNORECASE)
    
    # 去除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 去除可能的HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    
    return text.strip()

def download_nature_images(url):
    """
    从Nature系列期刊文章页面下载所有图片
    
    :param url: Nature文章页面URL
    :return: 下载的图片数量
    """
    print(f"▶️ 开始处理URL: {url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.nature.com/'
    }
    
    try:
        # 获取文章页面内容
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 提取文章标题和DOI
        title = None
        doi = None
        abstract = None
        
        # 尝试不同的选择器以适应不同的Nature系列期刊网站
        title_selectors = [
            'h1.c-article-title',
            'h1.article-heading',
            'h1.u-h1',
            'h1.title',
            'h1[itemprop="headline"]',
            'h1.main-heading'
        ]
        
        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.text.strip()
                break
        
        if not title:
            title = "Untitled_Nature_Article"
        
        # 尝试不同的选择器获取DOI
        doi_selectors = [
            'a.c-article-identifiers__doi',
            'span[data-test="doi"]',
            'a[data-track-action="view doi"]',
            'a[data-doi]'
        ]
        
        for selector in doi_selectors:
            doi_elem = soup.select_one(selector)
            if doi_elem:
                doi = doi_elem.text.strip()
                if doi.lower().startswith('doi:'):
                    doi = doi[4:].strip()
                break
        
        # 提取摘要
        abstract_selectors = [
            'div#Abs1-content',
            'div.c-article-section__content p',
            'div[id^="Abs"]',
            'section.abstract',
            'div.abstract',
            'p.abstract',
            'div[id*="abstract"]',
            'div.c-article-body p:first-of-type',
            'meta[name="description"]',
            'div.c-article-section:first-of-type p',
            'div[data-test="abstract"]',
            'p[data-test="abstractText"]',
            'div#abstract-content',
            'div.article__abstract',
            'div.article-abstract'
        ]
        
        for selector in abstract_selectors:
            abstract_elem = soup.select_one(selector)
            if abstract_elem:
                if selector == 'meta[name="description"]' and 'content' in abstract_elem.attrs:
                    abstract = abstract_elem['content'].strip()
                else:
                    abstract = abstract_elem.text.strip()
                abstract = clean_abstract_text(abstract)
                # 如果摘要太短，可能不是真正的摘要
                if len(abstract) > 100:
                    break
        
        # 如果上面的选择器都没找到摘要，尝试查找包含"Abstract"的段落
        if not abstract or len(abstract) < 100:
            paragraphs = soup.select('p')
            for p in paragraphs:
                if 'Abstract' in p.text or 'abstract' in p.text.lower():
                    abstract = clean_abstract_text(p.text.strip())
                    if len(abstract) > 100:
                        break
        
        # 如果仍然没找到摘要，尝试查找文章的第一段
        if not abstract or len(abstract) < 100:
            # 尝试找到文章正文的第一段
            article_sections = soup.select('div.c-article-section')
            for section in article_sections:
                # 跳过摘要部分
                if 'abstract' in section.get('id', '').lower() or 'abstract' in section.get('class', [''])[0].lower():
                    continue
                
                # 获取第一个段落
                first_p = section.select_one('p')
                if first_p and len(first_p.text.strip()) > 100:
                    abstract = first_p.text.strip()
                    break
        
        # 从URL中提取文章ID
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.split('/')
        article_id = None
        
        # 尝试从URL路径中提取文章ID
        for part in path_parts:
            if re.match(r's\d+-\d+-\d+-\d+', part) or re.match(r'\d+$', part):
                article_id = part
                break
        
        # 如果没有找到ID，使用URL的最后一部分
        if not article_id:
            article_id = path_parts[-1] if path_parts else "unknown_article"
        
        print(f"📝 文章标题: {title}")
        print(f"🔑 文章ID: {article_id}")
        if doi:
            print(f"🔖 DOI: {doi}")
        if abstract:
            print(f"📄 成功提取摘要 ({len(abstract)} 字符)")
        
        # 创建文件夹 (使用文章ID作为文件夹名)
        short_title = title[:30] if len(title) > 30 else title
        folder_name = sanitize_filename(f"{article_id}_{short_title}")
        os.makedirs(folder_name, exist_ok=True)
        print(f"📂 已创建/指定文件夹: '{folder_name}'")
        
        # 保存文章信息
        info_path = os.path.join(folder_name, "article_info.txt")
        with open(info_path, 'w', encoding='utf-8') as f:
            f.write(f"标题: {title}\n")
            if doi:
                f.write(f"DOI: {doi}\n")
            f.write(f"URL: {url}\n")
            f.write(f"ID: {article_id}\n")
            if abstract:
                f.write(f"\n摘要:\n{abstract}\n")
        
        # 方法1: 查找图片页面链接
        figure_links = []
        
        # 查找图表链接 - 方式1: 通过"figures"标签页
        figures_tab_selectors = [
            'a[data-track-action="figures"]',
            'a[href*="figures"]',
            'a[href*="figure"]'
        ]
        
        for selector in figures_tab_selectors:
            figures_tab = soup.select_one(selector)
            if figures_tab and 'href' in figures_tab.attrs:
                figures_url = urljoin(url, figures_tab['href'])
                print(f"🔍 找到图表页面链接: {figures_url}")
                figure_links.append(figures_url)
                break
        
        # 查找图表链接 - 方式2: 通过文章内的图片链接
        fig_link_selectors = [
            'a.c-article-section__figure-link',
            'a[href*="figure"]',
            'a[data-track-action*="figure"]',
            'a.figure-link'
        ]
        
        for selector in fig_link_selectors:
            fig_links = soup.select(selector)
            for link in fig_links:
                if 'href' in link.attrs:
                    fig_url = urljoin(url, link['href'])
                    if fig_url not in figure_links:
                        figure_links.append(fig_url)
        
        # 方法2: 直接在当前页面查找图片
        image_selectors = [
            'figure img.carousel-content-img',
            'div.c-article-section__figure img',
            'figure.c-article-figure img',
            'figure img',
            'div.figure img',
            'div.article-figure img',
            'img[data-test="figure"]'
        ]
        
        image_elements = []
        for selector in image_selectors:
            image_elements += soup.select(selector)
        
        # 下载图片计数
        downloaded_count = 0
        downloaded_urls = set()  # 用于跟踪已下载的URL，避免重复
        
        # 处理当前页面的图片
        for img in image_elements:
            if 'src' in img.attrs:
                img_url = img['src']
                if not img_url.startswith(('http://', 'https://')):
                    img_url = urljoin(url, img_url)
                
                # 尝试获取高分辨率版本
                if '?' in img_url:
                    img_url = img_url.split('?')[0]
                
                # 如果这个URL已经下载过，跳过
                if img_url in downloaded_urls:
                    continue
                
                try:
                    # 获取图片标题或编号
                    img_title = None
                    fig_number = None
                    
                    # 尝试从父元素获取图片编号和标题
                    fig_parent = img.find_parent('figure')
                    if fig_parent:
                        # 尝试查找图表编号
                        for selector in ['.c-article-figure__title', '.figure-title', 'figcaption strong', 'figcaption b']:
                            label_elem = fig_parent.select_one(selector)
                            if label_elem:
                                label_text = label_elem.text.strip()
                                # 提取图表编号 (例如 "Fig. 1", "Figure 2" 等)
                                fig_match = re.search(r'(?:fig(?:ure|\.)?)\s*(\d+[a-z]?)', label_text, re.IGNORECASE)
                                if fig_match:
                                    fig_number = fig_match.group(1)
                                    break
                        
                        # 尝试获取图表标题
                        caption = fig_parent.select_one('figcaption')
                        if caption:
                            # 移除图表编号部分
                            caption_text = caption.text.strip()
                            title_match = re.search(r'(?:fig(?:ure|\.)?)\s*\d+[a-z]?\s*[\.|\|]\s*(.*)', caption_text, re.IGNORECASE)
                            if title_match:
                                img_title = title_match.group(1).strip()
                    
                    # 如果没有找到图表编号，尝试从URL或其他属性中提取
                    if not fig_number:
                        # 尝试从URL中提取
                        url_match = re.search(r'Fig(\d+[a-z]?)', img_url, re.IGNORECASE)
                        if url_match:
                            fig_number = url_match.group(1)
                        else:
                            # 尝试从alt或title属性中提取
                            for attr in ['alt', 'title']:
                                if attr in img.attrs:
                                    attr_match = re.search(r'(?:fig(?:ure|\.)?)\s*(\d+[a-z]?)', img[attr], re.IGNORECASE)
                                    if attr_match:
                                        fig_number = attr_match.group(1)
                                        break
                    
                    # 如果仍然没有找到编号，使用计数器
                    if not fig_number:
                        fig_number = str(downloaded_count + 1)
                    
                    # 构造文件名
                    if img_title:
                        # 限制标题长度，避免文件名过长
                        short_img_title = img_title[:50] if len(img_title) > 50 else img_title
                        base_filename = f"Fig{fig_number}_{sanitize_filename(short_img_title)}"
                    else:
                        base_filename = f"Fig{fig_number}"
                    
                    # 从URL中获取文件扩展名
                    file_ext = os.path.splitext(urlparse(img_url).path)[1]
                    if not file_ext or len(file_ext) > 5:  # 防止异常的扩展名
                        file_ext = '.jpg'  # 默认扩展名
                    
                    # 构造文件名
                    filename = sanitize_filename(f"{base_filename}{file_ext}")
                    save_path = os.path.join(folder_name, filename)
                    
                    # 下载图片
                    print(f"⬇️ 正在下载图片: {img_url}")
                    img_response = requests.get(img_url, headers=headers, stream=True, timeout=15)
                    img_response.raise_for_status()
                    
                    with open(save_path, 'wb') as f:
                        for chunk in img_response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    
                    print(f"   ✅ 下载成功: {filename}")
                    downloaded_count += 1
                    downloaded_urls.add(img_url)  # 记录已下载的URL
                    
                    # 避免请求过于频繁
                    time.sleep(0.5)
                    
                except Exception as e:
                    print(f"   ❌ 下载失败: {img_url} | 原因: {e}")
        
        # 访问图片页面获取更多图片
        for fig_page_url in figure_links:
            try:
                print(f"🔍 访问图表页面: {fig_page_url}")
                fig_response = requests.get(fig_page_url, headers=headers, timeout=30)
                fig_response.raise_for_status()
                
                fig_soup = BeautifulSoup(fig_response.text, 'html.parser')
                
                # 查找图片元素
                fig_image_selectors = [
                    'figure img',
                    'div.c-article-section__figure img',
                    'div.carousel-item img',
                    'div.figure img',
                    'img.figure'
                ]
                
                fig_images = []
                for selector in fig_image_selectors:
                    fig_images += fig_soup.select(selector)
                
                for img in fig_images:
                    if 'src' in img.attrs:
                        img_url = img['src']
                        if not img_url.startswith(('http://', 'https://')):
                            img_url = urljoin(fig_page_url, img_url)
                        
                        # 尝试获取高分辨率版本
                        if '?' in img_url:
                            img_url = img_url.split('?')[0]
                        
                        # 尝试将缩略图URL替换为高分辨率版本
                        img_url = img_url.replace('/lw685/', '/full/')
                        
                        # 如果这个URL已经下载过，跳过
                        if img_url in downloaded_urls:
                            continue
                        
                        try:
                            # 获取图片标题或编号
                            img_title = None
                            fig_number = None
                            
                            # 尝试从父元素获取图片编号和标题
                            fig_parent = img.find_parent('figure')
                            if fig_parent:
                                # 尝试查找图表编号
                                for selector in ['.c-article-figure__title', '.figure-title', 'figcaption strong', 'figcaption b']:
                                    label_elem = fig_parent.select_one(selector)
                                    if label_elem:
                                        label_text = label_elem.text.strip()
                                        # 提取图表编号 (例如 "Fig. 1", "Figure 2" 等)
                                        fig_match = re.search(r'(?:fig(?:ure|\.)?)\s*(\d+[a-z]?)', label_text, re.IGNORECASE)
                                        if fig_match:
                                            fig_number = fig_match.group(1)
                                            break
                                
                                # 尝试获取图表标题
                                caption = fig_parent.select_one('figcaption')
                                if caption:
                                    # 移除图表编号部分
                                    caption_text = caption.text.strip()
                                    title_match = re.search(r'(?:fig(?:ure|\.)?)\s*\d+[a-z]?\s*[\.|\|]\s*(.*)', caption_text, re.IGNORECASE)
                                    if title_match:
                                        img_title = title_match.group(1).strip()
                            
                            # 如果没有找到图表编号，尝试从URL中提取
                            if not fig_number:
                                # 尝试从URL中提取
                                url_match = re.search(r'Fig(\d+)', img_url, re.IGNORECASE)
                                if url_match:
                                    fig_number = url_match.group(1)
                                else:
                                    # 尝试从页面URL中提取
                                    page_match = re.search(r'figures/(\d+)', fig_page_url)
                                    if page_match:
                                        fig_number = page_match.group(1)
                            
                            # 如果仍然没有找到编号，使用计数器
                            if not fig_number:
                                fig_number = str(downloaded_count + 1)
                            
                            # 构造文件名
                            if img_title:
                                # 限制标题长度，避免文件名过长
                                short_img_title = img_title[:50] if len(img_title) > 50 else img_title
                                base_filename = f"Fig{fig_number}_{sanitize_filename(short_img_title)}"
                            else:
                                base_filename = f"Fig{fig_number}"
                            
                            # 检查是否为高分辨率版本
                            if '/full/' in img_url:
                                base_filename += "_highres"
                            
                            # 从URL中获取文件扩展名
                            file_ext = os.path.splitext(urlparse(img_url).path)[1]
                            if not file_ext or len(file_ext) > 5:  # 防止异常的扩展名
                                file_ext = '.jpg'  # 默认扩展名
                            
                            # 构造文件名
                            filename = sanitize_filename(f"{base_filename}{file_ext}")
                            save_path = os.path.join(folder_name, filename)
                            
                            # 下载图片
                            print(f"⬇️ 正在下载图片: {img_url}")
                            img_response = requests.get(img_url, headers=headers, stream=True, timeout=15)
                            img_response.raise_for_status()
                            
                            with open(save_path, 'wb') as f:
                                for chunk in img_response.iter_content(chunk_size=8192):
                                    f.write(chunk)
                            
                            print(f"   ✅ 下载成功: {filename}")
                            downloaded_count += 1
                            downloaded_urls.add(img_url)  # 记录已下载的URL
                            
                            # 避免请求过于频繁
                            time.sleep(0.5)
                            
                        except Exception as e:
                            print(f"   ❌ 下载失败: {img_url} | 原因: {e}")
            
            except Exception as e:
                print(f"❌ 访问图表页面失败: {fig_page_url} | 原因: {e}")
        
        # 尝试查找API端点获取高分辨率图片
        try:
            api_url = f"https://www.nature.com/articles/article-data/figures/data/{article_id}"
            print(f"🔍 尝试从API获取图片数据: {api_url}")
            
            api_response = requests.get(api_url, headers=headers, timeout=15)
            if api_response.status_code == 200:
                try:
                    figures_data = api_response.json()
                    
                    for figure in figures_data.get('figures', []):
                        figure_id = figure.get('id')
                        figure_label = figure.get('label', f"figure_{downloaded_count + 1}")
                        
                        # 尝试获取最高分辨率的图片
                        image_url = None
                        for image in figure.get('images', []):
                            if image.get('type') == 'full':
                                image_url = image.get('source')
                                break
                        
                        if not image_url:
                            continue
                        
                        # 确保URL是完整的
                        if not image_url.startswith(('http://', 'https://')):
                            image_url = urljoin("https://www.nature.com", image_url)
                        
                        # 如果这个URL已经下载过，跳过
                        if image_url in downloaded_urls:
                            continue
                        
                        try:
                            # 提取图表编号
                            fig_number = None
                            fig_match = re.search(r'(?:fig(?:ure|\.)?)\s*(\d+[a-z]?)', figure_label, re.IGNORECASE)
                            if fig_match:
                                fig_number = fig_match.group(1)
                            else:
                                fig_number = str(downloaded_count + 1)
                            
                            # 从URL中获取文件扩展名
                            file_ext = os.path.splitext(urlparse(image_url).path)[1]
                            if not file_ext or len(file_ext) > 5:  # 防止异常的扩展名
                                file_ext = '.jpg'  # 默认扩展名
                            
                            # 构造文件名
                            filename = sanitize_filename(f"Fig{fig_number}_API_highres{file_ext}")
                            save_path = os.path.join(folder_name, filename)
                            
                            # 下载图片
                            print(f"⬇️ 正在下载高分辨率图片: {image_url}")
                            img_response = requests.get(image_url, headers=headers, stream=True, timeout=15)
                            img_response.raise_for_status()
                            
                            with open(save_path, 'wb') as f:
                                for chunk in img_response.iter_content(chunk_size=8192):
                                    f.write(chunk)
                            
                            print(f"   ✅ 下载成功: {filename}")
                            downloaded_count += 1
                            downloaded_urls.add(image_url)  # 记录已下载的URL
                            
                            # 避免请求过于频繁
                            time.sleep(0.5)
                            
                        except Exception as e:
                            print(f"   ❌ 下载失败: {image_url} | 原因: {e}")
                
                except json.JSONDecodeError:
                    print("⚠️ API返回的数据不是有效的JSON格式")
            else:
                print(f"⚠️ API请求失败，状态码: {api_response.status_code}")
        
        except Exception as e:
            print(f"⚠️ 尝试从API获取图片数据失败: {e}")
        
        print(f"\n🎉 处理完成！总共下载了 {downloaded_count} 张图片到文件夹 '{folder_name}'。")
        return downloaded_count
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 访问URL失败: {url} | 错误: {e}")
        return 0
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return 0

def download_arxiv_pdf(url):
    """
    下载arXiv论文的PDF文件
    
    :param url: arXiv论文URL (可以是摘要页或PDF链接)
    :return: 保存的PDF文件路径
    """
    print(f"▶️ 开始处理URL: {url}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # 提取arXiv ID
    arxiv_id = None
    match = re.search(r'(\d{4}\.\d{5}(v\d+)?)', url)
    if match:
        arxiv_id = match.group(1)
        print(f"ℹ️ 检测到ArXiv链接，ID: {arxiv_id}")
    else:
        print("❌ 无法从URL中提取arXiv ID")
        return None
    
    # 构造PDF URL
    pdf_url = f"https://arxiv.org/pdf/{arxiv_id}.pdf"
    print(f"🔍 尝试下载PDF: {pdf_url}")
    
    try:
        # 下载PDF
        response = requests.get(pdf_url, headers=headers, stream=True, timeout=30)
        response.raise_for_status()
        
        # 创建文件夹
        folder_name = arxiv_id
        os.makedirs(folder_name, exist_ok=True)
        print(f"📂 已创建/指定文件夹: '{folder_name}'")
        
        # 保存PDF
        pdf_path = os.path.join(folder_name, f"{arxiv_id}.pdf")
        with open(pdf_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ PDF下载成功: {pdf_path}")
        
        # 获取论文标题和摘要
        title = None
        abstract = None
        authors = None
        
        try:
            # 访问摘要页以获取标题
            abs_url = f"https://arxiv.org/abs/{arxiv_id}"
            abs_response = requests.get(abs_url, headers=headers, timeout=10)
            abs_response.raise_for_status()
            
            soup = BeautifulSoup(abs_response.text, 'html.parser')
            
            # 提取标题
            title_element = soup.select_one("h1.title")
            if title_element:
                title = title_element.text.replace("Title:", "").strip()
                print(f"📝 论文标题: {title}")
            
            # 提取作者
            authors_element = soup.select_one("div.authors")
            if authors_element:
                authors = authors_element.text.replace("Authors: <AUTHORS>
                print(f"👤 论文作者: {authors}")
            
            # 提取摘要
            abstract_element = soup.select_one("blockquote.abstract")
            if abstract_element:
                abstract = abstract_element.text.replace("Abstract:", "").strip()
                abstract = clean_abstract_text(abstract)
                print(f"📄 成功提取摘要 ({len(abstract)} 字符)")
            
            # 创建一个包含标题的文本文件
            title_file = os.path.join(folder_name, "article_info.txt")
            with open(title_file, 'w', encoding='utf-8') as f:
                if title:
                    f.write(f"标题: {title}\n")
                if authors:
                    f.write(f"作者: {authors}\n")
                f.write(f"arXiv ID: {arxiv_id}\n")
                f.write(f"PDF链接: {pdf_url}\n")
                f.write(f"摘要页: {abs_url}\n")
                if abstract:
                    f.write(f"\n摘要:\n{abstract}\n")
        except Exception as e:
            print(f"⚠️ 获取论文信息失败: {e}")
        
        return pdf_path
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 下载PDF失败: {pdf_url} | 原因: {e}")
        return None
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return None

# --- 使用示例 ---
if __name__ == "__main__":
    import sys
    
    # 解析命令行参数
    html_file = None
    base_url = None
    paper_url = None
    
    for i, arg in enumerate(sys.argv[1:]):
        if arg == '--html' and i + 1 < len(sys.argv) - 1:
            html_file = sys.argv[i + 2]
        elif arg == '--url' and i + 1 < len(sys.argv) - 1:
            base_url = sys.argv[i + 2]
            paper_url = base_url
    
    # 如果提供了HTML文件，从HTML文件处理
    if html_file:
        download_sciencedirect_from_html(html_file, base_url)
        sys.exit(0)
    
    # 默认示例URL，如果没有通过命令行提供
    if not paper_url:
        paper_url = "https://www.cell.com/joule/pdfExtended/S2542-4351(25)00191-6"  # Nature示例
        # 其它示例:
        # paper_url = "https://arxiv.org/abs/2406.17756"  # arXiv示例
        # paper_url = "https://www.nature.com/articles/s41586-023-06616-1"  # 另一个Nature示例
        # paper_url = "https://www.sciencedirect.com/science/article/pii/S1369702117306752"  # ScienceDirect示例
    
    print(f"📄 处理文章URL: {paper_url}")
    
    if "arxiv.org" in paper_url:
        pdf_path = download_arxiv_pdf(paper_url)
        if pdf_path:
            print(f"\n🎉 处理完成！PDF已保存到: {pdf_path}")
            print(f"\n💡 提示：您可以使用PDF阅读器打开文件，然后手动保存其中的图片")
    elif any(domain in paper_url for domain in ["nature.com", "scientificamerican.com", "springer.com"]):
        download_nature_images(paper_url)
    elif "sciencedirect.com" in paper_url:
        download_sciencedirect_images(paper_url)
        print("\n💡 ScienceDirect使用说明:")
        print("1. 使用浏览器打开文章页面")
        print("2. 右键点击网页，选择'另存为'或'Save as'，保存为HTML文件")
        print("3. 运行: python Article_fig_download.py --html 保存的文件名.html --url 原始文章URL")
        print("例如: python Article_fig_download.py --html saved_article.html --url https://www.sciencedirect.com/science/article/pii/S1369702117306752")
    else:
        print("⚠️ 不支持的URL。目前支持的网站: arXiv, Nature系列期刊, ScienceDirect")
        print("示例URL格式:")
        print("- arXiv: https://arxiv.org/abs/2406.17756")
        print("- Nature: https://www.nature.com/articles/s41586-023-06616-1")
        print("- ScienceDirect: https://www.sciencedirect.com/science/article/pii/S1369702117306752")
        print("- Scientific American: https://www.scientificamerican.com/article/xxx")
        print("- Springer: https://link.springer.com/article/xxx")
        print("\n对于ScienceDirect文章，可以使用本地HTML文件:")
        print("python Article_fig_download.py --html 保存的文件名.html --url 原始文章URL")