import numpy as np
import matplotlib as mpl
import matplotlib.pyplot as plt

def plot_beeswarm_panel_a(feature_impacts, feature_values,
                          feature_order=None,
                          output="panel_a_beeswarm",
                          figsize=(10, 2.2),  # 宽屏长条版面
                          xlim=(-25, 65)):
    """
    feature_impacts: dict[str, 1D array-like]  每行点的横坐标（影响值，单位 cycles）
    feature_values:  dict[str, 1D array-like]  每行点的着色值（任意范围，内部会归一化）
    feature_order:   list[str]                 绘制顺序（从上到下），默认使用 dict 顺序
    """

    # 排序（从上到下）
    if feature_order is None:
        feature_order = list(feature_impacts.keys())

    # 配色：浅灰 -> 渐变蓝（接近原图）
    cmap = mpl.colors.LinearSegmentedColormap.from_list(
        "grey_blue", ["#e9ecef", "#8fb7f0", "#3d7be0"], N=256
    )

    # 将所有着色值拼接，以便做全局归一化
    all_vals = np.concatenate([np.asarray(feature_values[f]).ravel()
                               for f in feature_order if len(feature_values[f]) > 0])
    # 若全为常数，避免除零
    vmin, vmax = float(np.min(all_vals)), float(np.max(all_vals))
    if vmax - vmin < 1e-12:
        vmin, vmax = vmin - 0.5, vmax + 0.5
    norm = mpl.colors.Normalize(vmin=vmin, vmax=vmax)

    fig, ax = plt.subplots(figsize=figsize, dpi=200)
    # 让左侧长标签留出空间，右侧给色条留边
    plt.subplots_adjust(left=0.30, right=0.86, top=0.92, bottom=0.28)

    # 纵坐标：从上到下
    y_positions = np.arange(len(feature_order))[::-1]

    # 垂直抖动幅度（控制“蜂群”厚度）
    jitter = 0.28
    dot_size = 18  # 点尺寸
    alpha_base = 0.9

    for yi, feat in zip(y_positions, feature_order):
        x = np.asarray(feature_impacts[feat]).ravel()
        cvals = np.asarray(feature_values[feat]).ravel()

        # 与 x 同长度的抖动
        y = yi + (np.random.rand(len(x)) - 0.5) * 2 * jitter

        ax.scatter(
            x, y,
            c=cvals, cmap=cmap, norm=norm,
            s=dot_size, alpha=alpha_base, linewidths=0,
        )

    # x=0 参照线
    ax.axvline(0, color="#8e8e8e", lw=1.0, alpha=0.8, zorder=0)

    # 网格（水平虚线，淡灰）
    ax.grid(axis="y", color="#d0d7de", linestyle="--", linewidth=0.8, alpha=0.6)
    ax.grid(axis="x", visible=False)

    # 轴范围与刻度
    ax.set_xlim(*xlim)
    # 只显示几个关键刻度，接近示例图
    ax.set_xticks([-20, 0, 20, 40, 60])
    ax.set_xlabel("Impact on EFC prediction (cycles)", fontsize=10)

    ax.set_yticks(y_positions)
    ax.set_yticklabels(feature_order, fontsize=9)

    # 去除上右边框，左下变淡
    for spine in ["top", "right"]:
        ax.spines[spine].set_visible(False)
    for spine in ["left", "bottom"]:
        ax.spines[spine].set_color("#b9c0c7")
        ax.spines[spine].set_linewidth(0.8)

    # 色条（竖直，右侧，标注“Feature value”，上下标 Low/High）
    mappable = mpl.cm.ScalarMappable(norm=norm, cmap=cmap)
    cbar = fig.colorbar(mappable, ax=ax, pad=0.02, fraction=0.08, aspect=30)
    cbar.set_label("Feature value", rotation=90, fontsize=9)
    # 只给出 Low / High 两端标注
    cbar.set_ticks([norm.vmin, norm.vmax])
    cbar.set_ticklabels(["Low", "High"])
    cbar.ax.tick_params(labelsize=8)

    # 紧致布局与导出
    plt.tight_layout()
    for ext in ["png", "svg", "pdf"]:
        plt.savefig(f"{output}.{ext}", dpi=600 if ext == "png" else None,
                    bbox_inches="tight")
    plt.show()


if __name__ == "__main__":
    # ===== 示例数据（用于预览，替换为你的真实数据即可）=====
    rng = np.random.default_rng(42)
    features = [
        "Maximum instantaneous discharge current",
        "Rest fraction at high SOC",
        r"Peak frequency $f_1$",
        "Rest SOC",
        "Normalized current variance",
        "Relative charge fraction",
        r"Peak frequency $f_2$",
    ]

    # 每个特征 60 个点，均值/方差设置为与示例图相近的分布形态
    means = [35, 18, 12, 5, 15, 22, -5]
    stds  = [7, 10, 9, 8, 8, 7, 6]

    feature_impacts = {}
    feature_values  = {}
    for feat, mu, sd in zip(features, means, stds):
        x = rng.normal(mu, sd, size=60)
        # 让一部分点落到负区间，增强对称性
        if mu > 0:
            x[:10] -= (abs(mu) + 15)

        # 特征值（用于着色），0-1 内分布；与 x 略有关联，提升“颜色趋势”效果
        raw_val = rng.normal(0.5 + 0.015 * (x - mu), 0.18, size=x.size)
        vals = np.clip(raw_val, 0, 1)

        feature_impacts[feat] = x
        feature_values[feat] = vals

    plot_beeswarm_panel_a(feature_impacts, feature_values,
                          feature_order=features,
                          output="figure_a_beeswarm",
                          figsize=(10, 2.2),
                          xlim=(-25, 65))
    
"""
    对这张“蜂群/抖动散点图”，真实数据应来自你模型对每个样本、每个特征的“单样本贡献值”（最推荐 SHAP 值），并用该样本的“原始特征值”做颜色。也就是说：

    横坐标 x = 每个样本在该特征上的影响值（建议用 SHAP，单位应与模型输出一致，如 cycles）
    颜色 c = 同一批样本的该特征原始数值（或经适当变换后的数值）
    用什么数据
    回归模型（推荐）：使用 SHAP regression 值，单位与模型预测一致（例：EFC cycles）
    线性/广义线性模型：也可用 per-sample effect ≈ x_j · coef_j（加上必要的链接函数/缩放）
    其他模型（树、Boosting、NN）：用 SHAP/DeepSHAP 等能给到“每样本每特征贡献”的方法
    不适合的数据：

    仅有“每特征一个数”的重要性（如 permutation importance、feature_importances_）不行，因为这图需要每个样本的一串点。
    数据结构与条件（对 shap_fig.py）
    feature_impacts: dict[str, 1D array]，键为特征名，值为“该特征对每个样本的影响值”数组
    feature_values: dict[str, 1D array]，同样键和值长度需与上面一一对应，用来着色
    必须条件
    两个字典的键集合一致
    对每个特征，feature_impacts[feat] 与 feature_values[feat] 的长度完全相同
    数值型，且尽量无 NaN/inf（建议先清洗/过滤）
    建议
    使用验证集/测试集样本，点数每行 50–500 比较合适
    颜色用原始值更易解释；动态范围极大时可用对数/标准化（函数内部会做颜色归一化）
    保证影响值单位与坐标轴一致；若你的模型不是“cycles”，要改图中 xlabel 或传入合适的单位范围 xlim
    典型获取方式（以树模型回归 + SHAP 为例）
    目标：EFC（cycles）回归；特征如 “Rest SOC / Peak frequency f1/f2 …”
    步骤：对 X_eval 计算 SHAP 值，横坐标取 shap_values，颜色取 X_eval 的原始特征值
import shap
import numpy as np
import pandas as pd

# 已训练回归模型 model；评估集特征 X_eval: pd.DataFrame，列顺序为特征顺序
explainer = shap.TreeExplainer(model)        # 树模型；其他模型用 KernelExplainer/DeepExplainer
shap_values = explainer.shap_values(X_eval)  # (n_samples, n_features) 回归返回 2D

feature_names = list(X_eval.columns)
feature_impacts = {feat: shap_values[:, i] for i, feat in enumerate(feature_names)}
feature_values  = {feat: X_eval.iloc[:, i].to_numpy() for i, feat in enumerate(feature_names)}

# 可按平均|SHAP|排序选择前若干特征
order_idx = np.argsort(np.mean(np.abs(shap_values), axis=0))[::-1]
top_feats = [feature_names[i] for i in order_idx[:7]]  # 选7个与示例一致

from shap_fig import plot_beeswarm_panel_a
plot_beeswarm_panel_a(
    feature_impacts={f: feature_impacts[f] for f in top_feats},
    feature_values={f: feature_values[f] for f in top_feats},
    feature_order=top_feats,
    output="figure_a_beeswarm_real",
    figsize=(10, 2.2),
    xlim=(-25, 65)  # 依据你的 SHAP 值范围微调
)
数据应具备的特征（质量与可解释性）
一致性：所有特征使用同一批样本的贡献值与原始值
单位可解释：如果目标是 cycles，SHAP 值应是“对 cycles 的增减贡献”；若是分类/对数几率，请确认是否要转换或改坐标/标签
分布合理：每行散点既有正也有负更易读；异常极端点可适度裁剪或设置合适的 xlim
特征值可读：颜色梯度能反映“值高/低时贡献方向的差异”；对偏态数据可做 log1p 后着色
常见坑
分类模型默认 SHAP 输出单位可能是 log-odds，而不是概率或 cycles；需要确认单位或调整 xlabel
键名不一致/长度不一致会报错或错位
全常数的颜色值会导致色条无梯度（代码已做保护，但建议提供有信息量的取值）
使用训练集而非评估/测试集，可能夸大可视化意义
若你给到模型与一小段样本数据，我可以帮你算出 SHAP 值并把这张图跑通、轴范围和配色也一并对齐。
"""