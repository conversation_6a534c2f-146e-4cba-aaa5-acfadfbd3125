# -*- coding: utf-8 -*-
"""
SHAP 风格组合图生成器（左：灰色条形+蜂群散点；右：6 个依赖图）
- 直接生成模拟数据，无需外部数据文件
- 自动选择可用模型（XGBoost 或 sklearn GradientBoosting）
- 兼容 SHAP 或使用 permutation importance 替代
- 完美复刻示例图的版式和配色

直接运行即可生成图片。
"""

import os
import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec

from sklearn.model_selection import train_test_split
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.inspection import permutation_importance
from scipy.signal import savgol_filter

# 字体
plt.rcParams['font.family'] = 'Times New Roman'

# 可选：在需要 GUI 交互时启用 TkAgg；如在服务器无显示环境请改为 'Agg'
try:
    mpl.use("TkAgg")
except Exception:
    mpl.use("Agg")

# ===================== CONFIG =====================
TARGET_COL = "PCM"  # 目标列名
RANDOM_STATE = 42
N_SAMPLES = 1000
N_FEATURES = 20

# 画图美学参数（对齐示例图）
AESTH = dict(
    ax_label_size=16,
    tick_label_size=12,
    legend_size=10,
    # 左侧颜色条（Feature value）
    summary_cbar_width=0.015,
    summary_cbar_height_shrink=1.0,
    summary_cbar_pad=0.006,
    # 右侧依赖图颜色条（PCM）
    dep_cbar_width=0.006,
    dep_cbar_height_shrink=0.90,
    dep_cbar_pad=0.003,
    dep_cbar_tick_length=1,
    # 子图网格间距
    grid_wspace=0.45,
    grid_hspace=0.40,
)
TOP_K_DEP = 6  # 依赖图展示的特征数
OUTPUT_PATH = "shap_analysis_plot.png"
# ==================================================


def generate_mock_data(n_samples=N_SAMPLES, n_features=N_FEATURES):
    """生成模拟数据，模仿示例图中的特征名称"""
    print(f"--> 生成模拟数据 ({n_samples} 样本, {n_features} 特征)...")
    np.random.seed(RANDOM_STATE)
    
    # 生成特征名称（模拟示例图中的特征）
    feature_names = [
        "Park_area", "Park_builttup", "Buffer_builttup", "Park_water", "Park_grass",
        "Park_tree", "Buffer_BD", "Park_perimeter", "Buffer_tree", "Buffer_MBH",
        "Buffer_DEM", "Buffer_grass", "Buffer_slope", "Buffer_road", "Park_LSI",
        "Park_slope", "Park_DEM", "Buffer_water", "Buffer_GDP", "Distance_water"
    ]
    
    # 生成随机特征数据
    X = np.random.randn(n_samples, n_features)
    
    # 生成目标变量（模拟 PCM 值，范围 1-7）
    y = 1 + 6 * (1 / (1 + np.exp(-(X[:, 0] + 0.5 * X[:, 1] - 0.3 * X[:, 2] + np.random.randn(n_samples) * 0.1))))
    
    # 创建 DataFrame
    df = pd.DataFrame(X, columns=feature_names[:n_features])
    df[TARGET_COL] = y
    
    print("数据预览:")
    print(df.head())
    return df[feature_names[:n_features]], df[TARGET_COL]


def train_model(X_train, y_train):
    """训练模型（优先 XGBoost，否则 GradientBoosting）"""
    try:
        from xgboost import XGBRegressor
        print("--> 使用 XGBRegressor...")
        model = XGBRegressor(n_estimators=300, max_depth=5, learning_rate=0.1, random_state=RANDOM_STATE)
    except ImportError:
        print("--> XGBoost 不可用，使用 GradientBoostingRegressor...")
        model = GradientBoostingRegressor(n_estimators=300, max_depth=5, learning_rate=0.1, random_state=RANDOM_STATE)
    
    model.fit(X_train, y_train)
    return model


def compute_shap_or_alternative(model, X_train, X_test, y_test):
    """计算 SHAP 值或使用 permutation importance 替代"""
    try:
        import shap
        print("--> 计算 SHAP 值...")
        explainer = shap.TreeExplainer(model)
        shap_values = explainer.shap_values(X_test)
        if hasattr(shap_values, 'values'):
            shap_mat = shap_values.values
        else:
            shap_mat = shap_values
        if shap_mat.ndim == 3:
            shap_mat = shap_mat.mean(axis=2)
        return shap_mat
    except Exception as e:
        print(f"--> SHAP 不可用 ({e})，使用 permutation importance 替代...")
        perm_result = permutation_importance(model, X_test, y_test, n_repeats=10, random_state=RANDOM_STATE)
        
        # 创建伪 SHAP 矩阵
        n_samples, n_features = X_test.shape
        shap_mat = np.zeros((n_samples, n_features))
        for i in range(n_features):
            base_importance = perm_result.importances_mean[i]
            std_dev = max(0.01, abs(base_importance) * 0.3)
            shap_mat[:, i] = np.random.normal(base_importance, std_dev, n_samples)
        
        print("注意：显示的是基于 permutation importance 的近似值")
        return shap_mat


def find_knee_point(x_data, y_data, window_length=11, polyorder=3):
    """基于 Savitzky-Golay 的二阶导最大值作为拐点"""
    x, y = np.asarray(x_data).ravel(), np.asarray(y_data).ravel()
    if len(x) < 5:
        return np.median(x)
    # 按 x 排序
    idx = np.argsort(x)
    xs, ys = x[idx], y[idx]
    # 调整窗口为奇数且不超过长度
    window_length = max(5, min(window_length, (len(xs) // 2) * 2 + 1))
    polyorder = min(polyorder, window_length - 1)
    try:
        d2 = savgol_filter(ys, window_length=window_length, polyorder=polyorder, deriv=2)
        knee_idx = int(np.argmax(np.abs(d2)))
        return xs[knee_idx]
    except Exception:
        # 回退：二阶差分极值
        d2_simple = np.gradient(np.gradient(ys))
        return xs[int(np.argmax(np.abs(d2_simple)))]


def plot_composite(X_test, y_test, shap_mat, feature_names, output_path=OUTPUT_PATH, top_k_dep=TOP_K_DEP):
    """绘制组合图"""
    # 计算重要性与排序（从小到大便于水平条形图）
    mean_abs = np.mean(np.abs(shap_mat), axis=0)
    order = np.argsort(mean_abs)  # 升序

    # 画布与网格
    fig = plt.figure(figsize=(20, 15), dpi=200)
    gs = gridspec.GridSpec(3, 4, figure=fig, wspace=AESTH["grid_wspace"], hspace=AESTH["grid_hspace"])

    # 左侧：蜂群 + 顶部灰条
    ax_main = fig.add_subplot(gs[:, :2])
    ax_top = ax_main.twiny()

    ordered_features = [feature_names[i] for i in order]
    y_positions = np.arange(len(ordered_features))

    # 顶部水平灰条（mean |SHAP|）
    ax_top.barh(y_positions, mean_abs[order], color="#d8dde6", alpha=1.0, height=0.7)
    ax_top.set_yticks(y_positions)
    ax_top.set_yticklabels(ordered_features, fontsize=AESTH["tick_label_size"])
    ax_top.set_xlabel("Mean Shapley Value (Feature Importance)", fontsize=AESTH["ax_label_size"])
    ax_top.grid(False)

    # 蜂群散点：颜色为每特征的 min-max 标准化，色谱 PiYG（绿-粉）
    cmap_fg = mpl.cm.get_cmap("PiYG")
    jitter = 0.10
    for i, feat in enumerate(ordered_features):
        j = feature_names.index(feat)
        x_vals = shap_mat[:, j]
        # 为颜色做每特征 min-max 标准化
        fvals = X_test.iloc[:, j].to_numpy()
        fmin, fmax = np.nanmin(fvals), np.nanmax(fvals)
        if np.isfinite(fmin) and np.isfinite(fmax) and fmax > fmin:
            cvals = (fvals - fmin) / (fmax - fmin)
        else:
            cvals = np.full_like(fvals, 0.5, dtype=float)
        y_jit = i + np.random.normal(0, jitter, size=x_vals.shape[0])
        ax_main.scatter(x_vals, y_jit, c=cvals, cmap=cmap_fg, s=14, alpha=0.9, zorder=2, linewidths=0)

    ax_main.set_yticks(y_positions)
    ax_main.set_yticklabels(ordered_features, fontsize=AESTH["tick_label_size"])
    ax_main.set_xlabel("SHAP value (impact on model output)", fontsize=AESTH["ax_label_size"])
    ax_main.grid(True, axis="x", linestyle="--", alpha=0.6)

    # 左侧颜色条（Feature value），仅标注 Low/High
    fig.canvas.draw()
    ax_pos = ax_main.get_position()
    cax_left = ax_pos.x1 + AESTH["summary_cbar_pad"]
    cax_bottom = ax_pos.y0 + (ax_pos.height * (1 - AESTH["summary_cbar_height_shrink"]) / 2)
    cax_width = AESTH["summary_cbar_width"]
    cax_height = ax_pos.height * AESTH["summary_cbar_height_shrink"]
    cax = fig.add_axes([cax_left, cax_bottom, cax_width, cax_height])
    sm = mpl.cm.ScalarMappable(norm=mpl.colors.Normalize(0, 1), cmap=cmap_fg)
    sm.set_array([])
    cbar = fig.colorbar(sm, cax=cax)
    cbar.set_label("Feature value", rotation=90, labelpad=-15, fontsize=AESTH["tick_label_size"])
    cbar.outline.set_visible(False)
    cbar.set_ticks([])
    # 添加 High/Low 文本
    cbar.ax.text(0.6, 1.02, "High", ha="center", va="bottom", transform=cbar.ax.transAxes, fontsize=AESTH["tick_label_size"])
    cbar.ax.text(0.6, -0.02, "Low", ha="center", va="top", transform=cbar.ax.transAxes, fontsize=AESTH["tick_label_size"])

    # 右侧：6 个依赖图（按重要性 Top-K，倒序展示）
    top_idx = list(np.argsort(mean_abs)[::-1][:top_k_dep])
    # 创建 3x2 的轴（占用右 2 列 * 3 行）
    axes_dep = []
    for i in range(3):
        for j in range(2):
            axes_dep.append(fig.add_subplot(gs[i, j + 2]))

    greens = mpl.cm.get_cmap("Greens")
    vmin, vmax = float(np.nanmin(y_test.values)), float(np.nanmax(y_test.values))

    for ax, fi in zip(axes_dep, top_idx):
        feat = feature_names[fi]
        x_feat = X_test.iloc[:, fi].values
        y_shap = shap_mat[:, fi]
        color_data = y_test.values
        sc = ax.scatter(x_feat, y_shap, c=color_data, cmap=greens, s=22, alpha=0.85, linewidths=0, vmin=vmin, vmax=vmax)

        # 拐点与中位线
        median_val = np.nanmedian(x_feat)
        thr_val = find_knee_point(x_feat, y_shap)
        ax.axvline(median_val, color="black", linestyle="--", linewidth=1)
        ax.axvline(thr_val, color="red", linestyle=":", linewidth=1.2)

        # 小图样式
        ax.set_title(feat, fontsize=12)
        ax.set_xlabel(feat, fontsize=AESTH["ax_label_size"])
        ax.set_ylabel("SHAP", fontsize=12, labelpad=-8)
        ax.axhline(0, color="#b0b0b0", lw=0.8)
        ax.grid(color="#eeeeee", ls=":", lw=0.8)
        ax.tick_params(axis="both", which="major", labelsize=AESTH["tick_label_size"])

        # 单幅色条，右侧细条，顶部标题"PCM"
        fig.canvas.draw()
        ax_pos = ax.get_position()
        cax_dep_left = ax_pos.x1 + AESTH["dep_cbar_pad"]
        cax_dep_bottom = ax_pos.y0 + (ax_pos.height * (1 - AESTH["dep_cbar_height_shrink"]) / 2)
        cax_dep_width = AESTH["dep_cbar_width"]
        cax_dep_height = ax_pos.height * AESTH["dep_cbar_height_shrink"]
        cax_dep = fig.add_axes([cax_dep_left, cax_dep_bottom, cax_dep_width, cax_dep_height])
        cbar = fig.colorbar(sc, cax=cax_dep)
        cbar.ax.set_title("PCM", fontsize=9)
        cbar.outline.set_visible(False)

        # 图例（Median/Threshold）
        from matplotlib.lines import Line2D
        line_handles = [
            Line2D([0], [0], color="black", lw=1, linestyle="--", label=f"Median: {median_val:.2f}"),
            Line2D([0], [0], color="red", lw=1, linestyle=":", label=f"Threshold: {thr_val:.2f}"),
        ]
        ax.legend(handles=line_handles, loc="best", fontsize=AESTH["legend_size"])

    # 保存
    os.makedirs(os.path.dirname(output_path) if os.path.dirname(output_path) else ".", exist_ok=True)
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    print(f"--> SHAP 组合图已成功保存到文件: {output_path}")
    plt.show()


def main():
    # 1) 生成模拟数据
    X, y = generate_mock_data()
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=RANDOM_STATE)

    # 2) 训练模型
    model = train_model(X_train, y_train)

    # 3) 计算 SHAP 值或替代
    shap_mat = compute_shap_or_alternative(model, X_train, X_test, y_test)

    # 4) 绘制组合图
    plot_composite(X_test.reset_index(drop=True), y_test.reset_index(drop=True), shap_mat, list(X.columns))


if __name__ == "__main__":
    main()
